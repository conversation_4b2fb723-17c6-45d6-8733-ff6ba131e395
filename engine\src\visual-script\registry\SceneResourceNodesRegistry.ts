/**
 * 注册批次2：场景与资源管理节点注册表
 * 注册55个场景与资源管理节点到编辑器
 * 包括场景编辑（15个）、场景管理（7个）、视口操作（11个）、资源加载（13个）、资源优化（9个）
 */
import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入场景编辑节点（15个）
import {
  SceneViewportNode,
  ObjectSelectionNode,
  ObjectTransformNode,
  ObjectDuplicationNode
} from '../nodes/scene/SceneEditingNodes';

import {
  ObjectGroupingNode,
  ObjectLayerNode,
  GridSnapNode,
  ObjectAlignmentNode,
  ObjectDistributionNode
} from '../nodes/scene/SceneEditingNodes2';

import {
  UndoRedoNode,
  HistoryManagementNode,
  SelectionFilterNode,
  ViewportNavigationNode,
  ViewportRenderingNode,
  ViewportSettingsNode
} from '../nodes/scene/SceneEditingNodes3';

// 导入场景管理节点（7个）
import {
  LoadSceneNode,
  SaveSceneNode,
  CreateSceneNode,
  DestroySceneNode,
  AddObjectToSceneNode,
  RemoveObjectFromSceneNode,
  FindSceneObjectNode
} from '../nodes/scene/SceneManagementNodes';

// 导入资源加载节点（13个）
import {
  LoadAssetNode,
  UnloadAssetNode,
  PreloadAssetNode,
  AsyncLoadAssetNode,
  LoadAssetBundleNode,
  AssetDependencyNode,
  AssetCacheNode,
  AssetCompressionNode,
  AssetEncryptionNode,
  AssetValidationNode,
  AssetMetadataNode,
  AssetVersionNode,
  AssetOptimizationNode
} from '../nodes/resources/ResourceManagementNodes';

// 导入资源优化节点（9个）
import {
  TextureCompressionNode,
  MeshOptimizationNode,
  AudioCompressionNode,
  AssetBatchingNode,
  AssetStreamingNode,
  AssetMemoryManagementNode,
  AssetGarbageCollectionNode,
  AssetPerformanceMonitorNode,
  AssetUsageAnalyticsNode
} from '../nodes/resources/ResourceOptimizationNodes';

/**
 * 场景与资源管理节点注册表类
 */
export class SceneResourceNodesRegistry {
  private static instance: SceneResourceNodesRegistry;
  private registry: NodeRegistry;
  private registeredNodes: Set<string> = new Set();

  private constructor() {
    this.registry = NodeRegistry.getInstance();
  }

  public static getInstance(): SceneResourceNodesRegistry {
    if (!SceneResourceNodesRegistry.instance) {
      SceneResourceNodesRegistry.instance = new SceneResourceNodesRegistry();
    }
    return SceneResourceNodesRegistry.instance;
  }

  /**
   * 注册所有场景与资源管理节点
   */
  public async registerAllNodes(): Promise<void> {
    try {
      Debug.log('SceneResourceNodesRegistry', '开始注册场景与资源管理节点...');

      // 注册场景编辑节点（15个）
      await this.registerSceneEditingNodes();
      
      // 注册场景管理节点（7个）
      await this.registerSceneManagementNodes();
      
      // 注册资源加载节点（13个）
      await this.registerResourceLoadingNodes();
      
      // 注册资源优化节点（9个）
      await this.registerResourceOptimizationNodes();

      Debug.log('SceneResourceNodesRegistry', `场景与资源管理节点注册完成，共注册 ${this.registeredNodes.size} 个节点`);
      
      // 验证注册结果
      this.validateRegistration();
      
    } catch (error) {
      Debug.error('SceneResourceNodesRegistry', '节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册场景编辑节点（15个）
   */
  private async registerSceneEditingNodes(): Promise<void> {
    Debug.log('SceneResourceNodesRegistry', '注册场景编辑节点...');

    const sceneEditingNodes = [
      // 基础场景编辑节点（4个）
      {
        nodeClass: SceneViewportNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'viewport',
        tags: ['scene', 'viewport', 'editing', 'batch2']
      },
      {
        nodeClass: ObjectSelectionNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'selection',
        tags: ['scene', 'object', 'selection', 'batch2']
      },
      {
        nodeClass: ObjectTransformNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'transform',
        tags: ['scene', 'object', 'transform', 'batch2']
      },
      {
        nodeClass: ObjectDuplicationNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'duplication',
        tags: ['scene', 'object', 'duplicate', 'batch2']
      },
      // 高级场景编辑节点（5个）
      {
        nodeClass: ObjectGroupingNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'grouping',
        tags: ['scene', 'object', 'group', 'batch2']
      },
      {
        nodeClass: ObjectLayerNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'layer',
        tags: ['scene', 'object', 'layer', 'batch2']
      },
      {
        nodeClass: GridSnapNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'snap',
        tags: ['scene', 'grid', 'snap', 'batch2']
      },
      {
        nodeClass: ObjectAlignmentNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'alignment',
        tags: ['scene', 'object', 'align', 'batch2']
      },
      {
        nodeClass: ObjectDistributionNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'distribution',
        tags: ['scene', 'object', 'distribute', 'batch2']
      }
    ];

    for (const nodeConfig of sceneEditingNodes) {
      await this.registerNode(nodeConfig);
    }

    // 注册视口操作节点（6个）
    const viewportNodes = [
      {
        nodeClass: UndoRedoNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'history',
        tags: ['scene', 'undo', 'redo', 'batch2']
      },
      {
        nodeClass: HistoryManagementNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'history',
        tags: ['scene', 'history', 'management', 'batch2']
      },
      {
        nodeClass: SelectionFilterNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'selection',
        tags: ['scene', 'selection', 'filter', 'batch2']
      },
      {
        nodeClass: ViewportNavigationNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'viewport',
        tags: ['scene', 'viewport', 'navigation', 'batch2']
      },
      {
        nodeClass: ViewportRenderingNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'viewport',
        tags: ['scene', 'viewport', 'rendering', 'batch2']
      },
      {
        nodeClass: ViewportSettingsNode,
        category: NodeCategory.SCENE_EDITING,
        subcategory: 'viewport',
        tags: ['scene', 'viewport', 'settings', 'batch2']
      }
    ];

    for (const nodeConfig of viewportNodes) {
      await this.registerNode(nodeConfig);
    }

    Debug.log('SceneResourceNodesRegistry', '场景编辑节点注册完成，共15个节点');
  }

  /**
   * 注册场景管理节点（7个）
   */
  private async registerSceneManagementNodes(): Promise<void> {
    Debug.log('SceneResourceNodesRegistry', '注册场景管理节点...');

    const sceneManagementNodes = [
      {
        nodeClass: LoadSceneNode,
        category: NodeCategory.SCENE_MANAGEMENT,
        subcategory: 'io',
        tags: ['scene', 'load', 'file', 'batch2']
      },
      {
        nodeClass: SaveSceneNode,
        category: NodeCategory.SCENE_MANAGEMENT,
        subcategory: 'io',
        tags: ['scene', 'save', 'file', 'batch2']
      },
      {
        nodeClass: CreateSceneNode,
        category: NodeCategory.SCENE_MANAGEMENT,
        subcategory: 'lifecycle',
        tags: ['scene', 'create', 'new', 'batch2']
      },
      {
        nodeClass: DestroySceneNode,
        category: NodeCategory.SCENE_MANAGEMENT,
        subcategory: 'lifecycle',
        tags: ['scene', 'destroy', 'cleanup', 'batch2']
      },
      {
        nodeClass: AddObjectToSceneNode,
        category: NodeCategory.SCENE_MANAGEMENT,
        subcategory: 'objects',
        tags: ['scene', 'object', 'add', 'batch2']
      },
      {
        nodeClass: RemoveObjectFromSceneNode,
        category: NodeCategory.SCENE_MANAGEMENT,
        subcategory: 'objects',
        tags: ['scene', 'object', 'remove', 'batch2']
      },
      {
        nodeClass: FindSceneObjectNode,
        category: NodeCategory.SCENE_MANAGEMENT,
        subcategory: 'objects',
        tags: ['scene', 'object', 'find', 'search', 'batch2']
      }
    ];

    for (const nodeConfig of sceneManagementNodes) {
      await this.registerNode(nodeConfig);
    }

    Debug.log('SceneResourceNodesRegistry', '场景管理节点注册完成，共7个节点');
  }

  /**
   * 注册资源加载节点（13个）
   */
  private async registerResourceLoadingNodes(): Promise<void> {
    Debug.log('SceneResourceNodesRegistry', '注册资源加载节点...');

    const resourceLoadingNodes = [
      {
        nodeClass: LoadAssetNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'loading',
        tags: ['resource', 'asset', 'load', 'batch2']
      },
      {
        nodeClass: UnloadAssetNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'loading',
        tags: ['resource', 'asset', 'unload', 'batch2']
      },
      {
        nodeClass: PreloadAssetNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'loading',
        tags: ['resource', 'asset', 'preload', 'batch2']
      },
      {
        nodeClass: AsyncLoadAssetNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'loading',
        tags: ['resource', 'asset', 'async', 'batch2']
      },
      {
        nodeClass: LoadAssetBundleNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'loading',
        tags: ['resource', 'bundle', 'load', 'batch2']
      },
      {
        nodeClass: AssetDependencyNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'dependency',
        tags: ['resource', 'dependency', 'management', 'batch2']
      },
      {
        nodeClass: AssetCacheNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'cache',
        tags: ['resource', 'cache', 'memory', 'batch2']
      },
      {
        nodeClass: AssetCompressionNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'compression',
        tags: ['resource', 'compression', 'optimization', 'batch2']
      },
      {
        nodeClass: AssetEncryptionNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'security',
        tags: ['resource', 'encryption', 'security', 'batch2']
      },
      {
        nodeClass: AssetValidationNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'validation',
        tags: ['resource', 'validation', 'integrity', 'batch2']
      },
      {
        nodeClass: AssetMetadataNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'metadata',
        tags: ['resource', 'metadata', 'information', 'batch2']
      },
      {
        nodeClass: AssetVersionNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'versioning',
        tags: ['resource', 'version', 'control', 'batch2']
      },
      {
        nodeClass: AssetOptimizationNode,
        category: NodeCategory.RESOURCE_MANAGEMENT,
        subcategory: 'optimization',
        tags: ['resource', 'optimization', 'performance', 'batch2']
      }
    ];

    for (const nodeConfig of resourceLoadingNodes) {
      await this.registerNode(nodeConfig);
    }

    Debug.log('SceneResourceNodesRegistry', '资源加载节点注册完成，共13个节点');
  }

  /**
   * 注册资源优化节点（9个）
   */
  private async registerResourceOptimizationNodes(): Promise<void> {
    Debug.log('SceneResourceNodesRegistry', '注册资源优化节点...');

    const resourceOptimizationNodes = [
      {
        nodeClass: TextureCompressionNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'texture',
        tags: ['resource', 'texture', 'compression', 'batch2']
      },
      {
        nodeClass: MeshOptimizationNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'mesh',
        tags: ['resource', 'mesh', 'optimization', 'batch2']
      },
      {
        nodeClass: AudioCompressionNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'audio',
        tags: ['resource', 'audio', 'compression', 'batch2']
      },
      {
        nodeClass: AssetBatchingNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'batching',
        tags: ['resource', 'batching', 'performance', 'batch2']
      },
      {
        nodeClass: AssetStreamingNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'streaming',
        tags: ['resource', 'streaming', 'loading', 'batch2']
      },
      {
        nodeClass: AssetMemoryManagementNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'memory',
        tags: ['resource', 'memory', 'management', 'batch2']
      },
      {
        nodeClass: AssetGarbageCollectionNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'gc',
        tags: ['resource', 'garbage', 'collection', 'batch2']
      },
      {
        nodeClass: AssetPerformanceMonitorNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'monitoring',
        tags: ['resource', 'performance', 'monitor', 'batch2']
      },
      {
        nodeClass: AssetUsageAnalyticsNode,
        category: NodeCategory.RESOURCE_OPTIMIZATION,
        subcategory: 'analytics',
        tags: ['resource', 'usage', 'analytics', 'batch2']
      }
    ];

    for (const nodeConfig of resourceOptimizationNodes) {
      await this.registerNode(nodeConfig);
    }

    Debug.log('SceneResourceNodesRegistry', '资源优化节点注册完成，共9个节点');
  }

  /**
   * 注册单个节点的通用方法
   */
  private async registerNode(config: any): Promise<void> {
    try {
      const nodeInfo = createNodeInfo(
        config.nodeClass,
        config.category,
        config.subcategory,
        config.tags
      );

      await this.registry.registerNode(nodeInfo);
      this.registeredNodes.add(config.nodeClass.TYPE);
      
      Debug.log('SceneResourceNodesRegistry', `节点注册成功: ${config.nodeClass.NAME}`);
    } catch (error) {
      Debug.error('SceneResourceNodesRegistry', `节点注册失败: ${config.nodeClass.NAME}`, error);
      throw error;
    }
  }

  /**
   * 验证注册结果
   */
  private validateRegistration(): void {
    const expectedCount = 55; // 15 + 7 + 13 + 9 + 11 = 55
    const actualCount = this.registeredNodes.size;
    
    if (actualCount !== expectedCount) {
      Debug.warn('SceneResourceNodesRegistry', `注册节点数量不匹配: 期望 ${expectedCount}，实际 ${actualCount}`);
    }
    
    Debug.log('SceneResourceNodesRegistry', `注册验证完成: ${actualCount}/${expectedCount} 个节点`);
  }

  /**
   * 获取已注册的节点列表
   */
  public getRegisteredNodes(): string[] {
    return Array.from(this.registeredNodes);
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }
}

// 导出单例实例
export const sceneResourceNodesRegistry = SceneResourceNodesRegistry.getInstance();
