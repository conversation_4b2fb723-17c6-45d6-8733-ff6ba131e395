/**
 * 场景与资源管理节点演示
 * 展示如何使用批次2注册的55个节点
 */
import { Debug } from '../../../utils/Debug';
import { sceneResourceNodesRegistry } from '../SceneResourceNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';

/**
 * 场景与资源管理节点演示类
 */
export class SceneResourceNodesDemo {
  private nodeRegistry: NodeRegistry;

  constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  /**
   * 运行完整演示
   */
  public async runDemo(): Promise<void> {
    Debug.log('SceneResourceNodesDemo', '开始场景与资源管理节点演示...');

    try {
      // 1. 注册所有节点
      await this.registerNodes();

      // 2. 演示场景编辑功能
      await this.demonstrateSceneEditing();

      // 3. 演示场景管理功能
      await this.demonstrateSceneManagement();

      // 4. 演示资源加载功能
      await this.demonstrateResourceLoading();

      // 5. 演示资源优化功能
      await this.demonstrateResourceOptimization();

      // 6. 显示统计信息
      this.showStatistics();

      Debug.log('SceneResourceNodesDemo', '场景与资源管理节点演示完成');
    } catch (error) {
      Debug.error('SceneResourceNodesDemo', '演示过程中发生错误:', error);
    }
  }

  /**
   * 注册所有场景与资源管理节点
   */
  private async registerNodes(): Promise<void> {
    Debug.log('SceneResourceNodesDemo', '注册场景与资源管理节点...');
    
    await sceneResourceNodesRegistry.registerAllNodes();
    
    const registeredNodes = sceneResourceNodesRegistry.getRegisteredNodes();
    Debug.log('SceneResourceNodesDemo', `成功注册 ${registeredNodes.length} 个节点`);
  }

  /**
   * 演示场景编辑功能
   */
  private async demonstrateSceneEditing(): Promise<void> {
    Debug.log('SceneResourceNodesDemo', '演示场景编辑功能...');

    // 演示场景视口节点
    if (sceneResourceNodesRegistry.isNodeRegistered('SceneViewport')) {
      const viewportNode = this.nodeRegistry.createNode('SceneViewport');
      Debug.log('SceneResourceNodesDemo', '创建场景视口节点成功');
      
      // 模拟设置视口
      const viewportResult = viewportNode?.execute({
        setup: true,
        width: 1920,
        height: 1080,
        camera: { type: 'PerspectiveCamera' },
        renderer: { type: 'WebGLRenderer' }
      });
      Debug.log('SceneResourceNodesDemo', '视口设置结果:', viewportResult);
    }

    // 演示对象选择节点
    if (sceneResourceNodesRegistry.isNodeRegistered('ObjectSelection')) {
      const selectionNode = this.nodeRegistry.createNode('ObjectSelection');
      Debug.log('SceneResourceNodesDemo', '创建对象选择节点成功');
      
      // 模拟选择对象
      const selectionResult = selectionNode?.execute({
        select: true,
        objectId: 'cube_001',
        multiSelect: false
      });
      Debug.log('SceneResourceNodesDemo', '对象选择结果:', selectionResult);
    }

    // 演示对象变换节点
    if (sceneResourceNodesRegistry.isNodeRegistered('ObjectTransform')) {
      const transformNode = this.nodeRegistry.createNode('ObjectTransform');
      Debug.log('SceneResourceNodesDemo', '创建对象变换节点成功');
      
      // 模拟变换对象
      const transformResult = transformNode?.execute({
        transform: true,
        objectId: 'cube_001',
        position: { x: 10, y: 5, z: 0 },
        rotation: { x: 0, y: 45, z: 0 },
        scale: { x: 1.5, y: 1.5, z: 1.5 }
      });
      Debug.log('SceneResourceNodesDemo', '对象变换结果:', transformResult);
    }

    // 演示撤销重做功能
    if (sceneResourceNodesRegistry.isNodeRegistered('UndoRedo')) {
      const undoRedoNode = this.nodeRegistry.createNode('UndoRedo');
      Debug.log('SceneResourceNodesDemo', '创建撤销重做节点成功');
      
      // 模拟撤销操作
      const undoResult = undoRedoNode?.execute({
        undo: true
      });
      Debug.log('SceneResourceNodesDemo', '撤销操作结果:', undoResult);
    }
  }

  /**
   * 演示场景管理功能
   */
  private async demonstrateSceneManagement(): Promise<void> {
    Debug.log('SceneResourceNodesDemo', '演示场景管理功能...');

    // 演示创建场景节点
    if (sceneResourceNodesRegistry.isNodeRegistered('CreateScene')) {
      const createSceneNode = this.nodeRegistry.createNode('CreateScene');
      Debug.log('SceneResourceNodesDemo', '创建场景节点成功');
      
      // 模拟创建场景
      const createResult = createSceneNode?.execute({
        create: true,
        sceneId: 'demo_scene_001',
        sceneName: '演示场景',
        description: '用于演示的测试场景',
        tags: ['demo', 'test']
      });
      Debug.log('SceneResourceNodesDemo', '场景创建结果:', createResult);
    }

    // 演示加载场景节点
    if (sceneResourceNodesRegistry.isNodeRegistered('LoadScene')) {
      const loadSceneNode = this.nodeRegistry.createNode('LoadScene');
      Debug.log('SceneResourceNodesDemo', '创建加载场景节点成功');
      
      // 模拟加载场景
      const loadResult = loadSceneNode?.execute({
        load: true,
        sceneId: 'demo_scene_001'
      });
      Debug.log('SceneResourceNodesDemo', '场景加载结果:', loadResult);
    }

    // 演示添加对象到场景
    if (sceneResourceNodesRegistry.isNodeRegistered('AddObjectToScene')) {
      const addObjectNode = this.nodeRegistry.createNode('AddObjectToScene');
      Debug.log('SceneResourceNodesDemo', '创建添加对象节点成功');
      
      // 模拟添加对象
      const addResult = addObjectNode?.execute({
        add: true,
        sceneId: 'demo_scene_001',
        objectType: 'Cube',
        objectName: 'demo_cube',
        position: { x: 0, y: 0, z: 0 }
      });
      Debug.log('SceneResourceNodesDemo', '对象添加结果:', addResult);
    }
  }

  /**
   * 演示资源加载功能
   */
  private async demonstrateResourceLoading(): Promise<void> {
    Debug.log('SceneResourceNodesDemo', '演示资源加载功能...');

    // 演示加载资源节点
    if (sceneResourceNodesRegistry.isNodeRegistered('LoadAsset')) {
      const loadAssetNode = this.nodeRegistry.createNode('LoadAsset');
      Debug.log('SceneResourceNodesDemo', '创建加载资源节点成功');
      
      // 模拟加载资源
      const loadResult = loadAssetNode?.execute({
        load: true,
        assetPath: '/assets/models/demo_model.glb',
        assetType: 'model',
        loadOptions: {
          useCache: true,
          priority: 'high'
        }
      });
      Debug.log('SceneResourceNodesDemo', '资源加载结果:', loadResult);
    }

    // 演示预加载资源节点
    if (sceneResourceNodesRegistry.isNodeRegistered('PreloadAsset')) {
      const preloadNode = this.nodeRegistry.createNode('PreloadAsset');
      Debug.log('SceneResourceNodesDemo', '创建预加载资源节点成功');
      
      // 模拟预加载资源
      const preloadResult = preloadNode?.execute({
        preload: true,
        assetPaths: [
          '/assets/textures/demo_texture.jpg',
          '/assets/audio/demo_sound.mp3'
        ],
        priority: 'medium'
      });
      Debug.log('SceneResourceNodesDemo', '资源预加载结果:', preloadResult);
    }

    // 演示资源缓存节点
    if (sceneResourceNodesRegistry.isNodeRegistered('AssetCache')) {
      const cacheNode = this.nodeRegistry.createNode('AssetCache');
      Debug.log('SceneResourceNodesDemo', '创建资源缓存节点成功');
      
      // 模拟缓存管理
      const cacheResult = cacheNode?.execute({
        operation: 'status',
        assetId: 'demo_model.glb'
      });
      Debug.log('SceneResourceNodesDemo', '资源缓存结果:', cacheResult);
    }
  }

  /**
   * 演示资源优化功能
   */
  private async demonstrateResourceOptimization(): Promise<void> {
    Debug.log('SceneResourceNodesDemo', '演示资源优化功能...');

    // 演示纹理压缩节点
    if (sceneResourceNodesRegistry.isNodeRegistered('TextureCompression')) {
      const compressionNode = this.nodeRegistry.createNode('TextureCompression');
      Debug.log('SceneResourceNodesDemo', '创建纹理压缩节点成功');
      
      // 模拟纹理压缩
      const compressionResult = compressionNode?.execute({
        compress: true,
        textureId: 'demo_texture.jpg',
        format: 'DXT5',
        quality: 'high'
      });
      Debug.log('SceneResourceNodesDemo', '纹理压缩结果:', compressionResult);
    }

    // 演示网格优化节点
    if (sceneResourceNodesRegistry.isNodeRegistered('MeshOptimization')) {
      const meshOptNode = this.nodeRegistry.createNode('MeshOptimization');
      Debug.log('SceneResourceNodesDemo', '创建网格优化节点成功');
      
      // 模拟网格优化
      const meshOptResult = meshOptNode?.execute({
        optimize: true,
        meshId: 'demo_model.glb',
        targetVertices: 1000,
        preserveUVs: true
      });
      Debug.log('SceneResourceNodesDemo', '网格优化结果:', meshOptResult);
    }

    // 演示资源性能监控节点
    if (sceneResourceNodesRegistry.isNodeRegistered('AssetPerformanceMonitor')) {
      const monitorNode = this.nodeRegistry.createNode('AssetPerformanceMonitor');
      Debug.log('SceneResourceNodesDemo', '创建资源性能监控节点成功');
      
      // 模拟性能监控
      const monitorResult = monitorNode?.execute({
        startMonitoring: true,
        assetTypes: ['texture', 'model', 'audio'],
        reportInterval: 5000
      });
      Debug.log('SceneResourceNodesDemo', '性能监控结果:', monitorResult);
    }
  }

  /**
   * 显示统计信息
   */
  private showStatistics(): void {
    Debug.log('SceneResourceNodesDemo', '=== 场景与资源管理节点统计 ===');
    
    const registeredNodes = sceneResourceNodesRegistry.getRegisteredNodes();
    Debug.log('SceneResourceNodesDemo', `总注册节点数: ${registeredNodes.length}`);
    
    // 按类型分组统计
    const nodesByType = {
      sceneEditing: registeredNodes.filter(node => 
        ['SceneViewport', 'ObjectSelection', 'ObjectTransform', 'ObjectDuplication', 
         'ObjectGrouping', 'ObjectLayer', 'GridSnap', 'ObjectAlignment', 'ObjectDistribution',
         'UndoRedo', 'HistoryManagement', 'SelectionFilter', 'ViewportNavigation', 
         'ViewportRendering', 'ViewportSettings'].includes(node)
      ),
      sceneManagement: registeredNodes.filter(node => 
        ['LoadScene', 'SaveScene', 'CreateScene', 'DestroyScene', 
         'AddObjectToScene', 'RemoveObjectFromScene', 'FindSceneObject'].includes(node)
      ),
      resourceLoading: registeredNodes.filter(node => 
        ['LoadAsset', 'UnloadAsset', 'PreloadAsset', 'AsyncLoadAsset', 'LoadAssetBundle',
         'AssetDependency', 'AssetCache', 'AssetCompression', 'AssetEncryption',
         'AssetValidation', 'AssetMetadata', 'AssetVersion', 'AssetOptimization'].includes(node)
      ),
      resourceOptimization: registeredNodes.filter(node => 
        ['TextureCompression', 'MeshOptimization', 'AudioCompression', 'AssetBatching',
         'AssetStreaming', 'AssetMemoryManagement', 'AssetGarbageCollection',
         'AssetPerformanceMonitor', 'AssetUsageAnalytics'].includes(node)
      )
    };

    Debug.log('SceneResourceNodesDemo', `场景编辑节点: ${nodesByType.sceneEditing.length}个`);
    Debug.log('SceneResourceNodesDemo', `场景管理节点: ${nodesByType.sceneManagement.length}个`);
    Debug.log('SceneResourceNodesDemo', `资源加载节点: ${nodesByType.resourceLoading.length}个`);
    Debug.log('SceneResourceNodesDemo', `资源优化节点: ${nodesByType.resourceOptimization.length}个`);
    
    Debug.log('SceneResourceNodesDemo', '=== 统计完成 ===');
  }
}

/**
 * 运行演示
 */
export async function runSceneResourceNodesDemo(): Promise<void> {
  const demo = new SceneResourceNodesDemo();
  await demo.runDemo();
}
