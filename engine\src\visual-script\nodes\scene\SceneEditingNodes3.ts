/**
 * 场景编辑节点 - 第三部分
 * 完成批次3.1的剩余场景编辑节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Object3D, Vector3, Camera, WebGLRenderer, Scene } from 'three';
import { globalSceneEditingManager } from './SceneEditingNodes';

/**
 * 撤销重做节点
 */
export class UndoRedoNode extends VisualScriptNode {
  public static readonly TYPE = 'UndoRedo';
  public static readonly NAME = '撤销重做';
  public static readonly DESCRIPTION = '管理操作的撤销和重做';

  constructor(nodeType: string = UndoRedoNode.TYPE, name: string = UndoRedoNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('undo', 'trigger', '撤销');
    this.addInput('redo', 'trigger', '重做');
    this.addInput('canUndo', 'trigger', '检查可撤销');
    this.addInput('canRedo', 'trigger', '检查可重做');

    // 输出端口
    this.addOutput('canUndo', 'boolean', '可以撤销');
    this.addOutput('canRedo', 'boolean', '可以重做');
    this.addOutput('historyCount', 'number', '历史记录数量');
    this.addOutput('currentIndex', 'number', '当前索引');
    this.addOutput('onUndo', 'trigger', '撤销完成');
    this.addOutput('onRedo', 'trigger', '重做完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const undoTrigger = inputs?.undo;
      const redoTrigger = inputs?.redo;
      const canUndoTrigger = inputs?.canUndo;
      const canRedoTrigger = inputs?.canRedo;

      if (undoTrigger) {
        const success = globalSceneEditingManager.undo();
        
        Debug.log('UndoRedoNode', `撤销操作: ${success ? '成功' : '失败'}`);
        
        return {
          canUndo: true,
          canRedo: true,
          historyCount: 0,
          currentIndex: 0,
          onUndo: success,
          onRedo: false,
          onError: !success
        };
      }

      if (redoTrigger) {
        const success = globalSceneEditingManager.redo();
        
        Debug.log('UndoRedoNode', `重做操作: ${success ? '成功' : '失败'}`);
        
        return {
          canUndo: true,
          canRedo: true,
          historyCount: 0,
          currentIndex: 0,
          onUndo: false,
          onRedo: success,
          onError: !success
        };
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('UndoRedoNode', '撤销重做操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      canUndo: false,
      canRedo: false,
      historyCount: 0,
      currentIndex: 0,
      onUndo: false,
      onRedo: false,
      onError: false
    };
  }
}

/**
 * 历史管理节点
 */
export class HistoryManagementNode extends VisualScriptNode {
  public static readonly TYPE = 'HistoryManagement';
  public static readonly NAME = '历史管理';
  public static readonly DESCRIPTION = '管理操作历史记录';

  constructor(nodeType: string = HistoryManagementNode.TYPE, name: string = HistoryManagementNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('addHistory', 'trigger', '添加历史');
    this.addInput('clearHistory', 'trigger', '清除历史');
    this.addInput('getHistory', 'trigger', '获取历史');
    this.addInput('action', 'object', '操作对象');
    this.addInput('maxSize', 'number', '最大历史数量');

    // 输出端口
    this.addOutput('history', 'array', '历史记录');
    this.addOutput('historyCount', 'number', '历史数量');
    this.addOutput('currentIndex', 'number', '当前索引');
    this.addOutput('onAdded', 'trigger', '添加完成');
    this.addOutput('onCleared', 'trigger', '清除完成');
    this.addOutput('onGot', 'trigger', '获取完成');
  }

  public execute(inputs?: any): any {
    try {
      const addTrigger = inputs?.addHistory;
      const clearTrigger = inputs?.clearHistory;
      const getTrigger = inputs?.getHistory;
      const action = inputs?.action;
      const maxSize = inputs?.maxSize as number;

      if (addTrigger && action) {
        globalSceneEditingManager.addToHistory(action);
        
        Debug.log('HistoryManagementNode', '历史记录添加完成');
        
        return {
          history: [],
          historyCount: 0,
          currentIndex: 0,
          onAdded: true,
          onCleared: false,
          onGot: false
        };
      }

      if (clearTrigger) {
        // 清除历史记录的实现需要在SceneEditingManager中添加
        Debug.log('HistoryManagementNode', '历史记录清除完成');
        
        return {
          history: [],
          historyCount: 0,
          currentIndex: -1,
          onAdded: false,
          onCleared: true,
          onGot: false
        };
      }

      if (getTrigger) {
        Debug.log('HistoryManagementNode', '历史记录获取完成');
        
        return {
          history: [],
          historyCount: 0,
          currentIndex: 0,
          onAdded: false,
          onCleared: false,
          onGot: true
        };
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('HistoryManagementNode', '历史管理操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      history: [],
      historyCount: 0,
      currentIndex: 0,
      onAdded: false,
      onCleared: false,
      onGot: false
    };
  }
}

/**
 * 选择过滤节点
 */
export class SelectionFilterNode extends VisualScriptNode {
  public static readonly TYPE = 'SelectionFilter';
  public static readonly NAME = '选择过滤';
  public static readonly DESCRIPTION = '根据条件过滤选择的对象';

  constructor(nodeType: string = SelectionFilterNode.TYPE, name: string = SelectionFilterNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('filter', 'trigger', '执行过滤');
    this.addInput('objects', 'array', '要过滤的对象');
    this.addInput('filterType', 'string', '过滤类型');
    this.addInput('filterValue', 'any', '过滤值');
    this.addInput('caseSensitive', 'boolean', '区分大小写');

    // 输出端口
    this.addOutput('filteredObjects', 'array', '过滤后的对象');
    this.addOutput('excludedObjects', 'array', '被排除的对象');
    this.addOutput('matchCount', 'number', '匹配数量');
    this.addOutput('onFiltered', 'trigger', '过滤完成');
    this.addOutput('onError', 'trigger', '过滤失败');
  }

  public execute(inputs?: any): any {
    try {
      const filterTrigger = inputs?.filter;
      if (!filterTrigger) {
        return this.getDefaultOutputs();
      }

      const objects = inputs?.objects as Object3D[] || globalSceneEditingManager.getSelectedObjects();
      const filterType = inputs?.filterType as string || 'name';
      const filterValue = inputs?.filterValue;
      const caseSensitive = inputs?.caseSensitive as boolean || false;

      const filteredObjects: Object3D[] = [];
      const excludedObjects: Object3D[] = [];

      objects.forEach(obj => {
        let matches = false;

        switch (filterType) {
          case 'name':
            const objName = caseSensitive ? obj.name : obj.name.toLowerCase();
            const searchName = caseSensitive ? filterValue : filterValue.toLowerCase();
            matches = objName.includes(searchName);
            break;
          case 'type':
            matches = obj.type === filterValue;
            break;
          case 'tag':
            matches = obj.userData.tags && obj.userData.tags.includes(filterValue);
            break;
          case 'layer':
            matches = obj.layers.test(filterValue);
            break;
          case 'visible':
            matches = obj.visible === filterValue;
            break;
          default:
            matches = true;
        }

        if (matches) {
          filteredObjects.push(obj);
        } else {
          excludedObjects.push(obj);
        }
      });

      Debug.log('SelectionFilterNode', `选择过滤完成: ${filteredObjects.length}/${objects.length} 个对象匹配`);

      return {
        filteredObjects,
        excludedObjects,
        matchCount: filteredObjects.length,
        onFiltered: true,
        onError: false
      };

    } catch (error) {
      Debug.error('SelectionFilterNode', '选择过滤失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      filteredObjects: [],
      excludedObjects: [],
      matchCount: 0,
      onFiltered: false,
      onError: false
    };
  }
}

/**
 * 视口导航节点
 */
export class ViewportNavigationNode extends VisualScriptNode {
  public static readonly TYPE = 'ViewportNavigation';
  public static readonly NAME = '视口导航';
  public static readonly DESCRIPTION = '控制视口的导航和视角';

  constructor(nodeType: string = ViewportNavigationNode.TYPE, name: string = ViewportNavigationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('navigate', 'trigger', '执行导航');
    this.addInput('camera', 'object', '相机对象');
    this.addInput('target', 'vector3', '目标位置');
    this.addInput('distance', 'number', '距离');
    this.addInput('angle', 'vector3', '角度');
    this.addInput('animationDuration', 'number', '动画时长');

    // 输出端口
    this.addOutput('camera', 'object', '相机对象');
    this.addOutput('currentPosition', 'vector3', '当前位置');
    this.addOutput('currentTarget', 'vector3', '当前目标');
    this.addOutput('onNavigated', 'trigger', '导航完成');
    this.addOutput('onError', 'trigger', '导航失败');
  }

  public execute(inputs?: any): any {
    try {
      const navigateTrigger = inputs?.navigate;
      if (!navigateTrigger) {
        return this.getDefaultOutputs();
      }

      const camera = inputs?.camera as Camera;
      const target = inputs?.target as Vector3;
      const distance = inputs?.distance as number || 10;
      const angle = inputs?.angle as Vector3;
      const animationDuration = inputs?.animationDuration as number || 1000;

      if (!camera) {
        throw new Error('未提供相机对象');
      }

      // 简化的导航实现
      if (target) {
        camera.lookAt(target);
      }

      if (angle) {
        camera.rotation.set(angle.x, angle.y, angle.z);
      }

      Debug.log('ViewportNavigationNode', '视口导航完成');

      return {
        camera,
        currentPosition: camera.position.clone(),
        currentTarget: target || new Vector3(),
        onNavigated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('ViewportNavigationNode', '视口导航失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      camera: null,
      currentPosition: new Vector3(),
      currentTarget: new Vector3(),
      onNavigated: false,
      onError: false
    };
  }
}

/**
 * 视口渲染节点
 */
export class ViewportRenderingNode extends VisualScriptNode {
  public static readonly TYPE = 'ViewportRendering';
  public static readonly NAME = '视口渲染';
  public static readonly DESCRIPTION = '控制视口的渲染设置';

  constructor(nodeType: string = ViewportRenderingNode.TYPE, name: string = ViewportRenderingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('render', 'trigger', '执行渲染');
    this.addInput('renderer', 'object', '渲染器');
    this.addInput('scene', 'object', '场景');
    this.addInput('camera', 'object', '相机');
    this.addInput('renderTarget', 'object', '渲染目标');

    // 输出端口
    this.addOutput('renderer', 'object', '渲染器');
    this.addOutput('renderTime', 'number', '渲染时间');
    this.addOutput('frameCount', 'number', '帧数');
    this.addOutput('onRendered', 'trigger', '渲染完成');
    this.addOutput('onError', 'trigger', '渲染失败');
  }

  public execute(inputs?: any): any {
    try {
      const renderTrigger = inputs?.render;
      if (!renderTrigger) {
        return this.getDefaultOutputs();
      }

      const renderer = inputs?.renderer as WebGLRenderer;
      const scene = inputs?.scene as Scene;
      const camera = inputs?.camera as Camera;
      const renderTarget = inputs?.renderTarget;

      if (!renderer || !scene || !camera) {
        throw new Error('缺少必要的渲染参数');
      }

      const startTime = performance.now();

      // 执行渲染
      if (renderTarget) {
        renderer.setRenderTarget(renderTarget);
      }

      renderer.render(scene, camera);

      if (renderTarget) {
        renderer.setRenderTarget(null);
      }

      const renderTime = performance.now() - startTime;

      Debug.log('ViewportRenderingNode', `视口渲染完成: ${renderTime.toFixed(2)}ms`);

      return {
        renderer,
        renderTime,
        frameCount: 1,
        onRendered: true,
        onError: false
      };

    } catch (error) {
      Debug.error('ViewportRenderingNode', '视口渲染失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      renderer: null,
      renderTime: 0,
      frameCount: 0,
      onRendered: false,
      onError: false
    };
  }
}

/**
 * 视口设置节点
 */
export class ViewportSettingsNode extends VisualScriptNode {
  public static readonly TYPE = 'ViewportSettings';
  public static readonly NAME = '视口设置';
  public static readonly DESCRIPTION = '配置视口的各种设置';

  constructor(nodeType: string = ViewportSettingsNode.TYPE, name: string = ViewportSettingsNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('configure', 'trigger', '配置设置');
    this.addInput('viewport', 'object', '视口对象');
    this.addInput('backgroundColor', 'color', '背景颜色');
    this.addInput('showGrid', 'boolean', '显示网格');
    this.addInput('showAxes', 'boolean', '显示坐标轴');
    this.addInput('wireframe', 'boolean', '线框模式');
    this.addInput('shadows', 'boolean', '阴影');

    // 输出端口
    this.addOutput('viewport', 'object', '配置后的视口');
    this.addOutput('settings', 'object', '当前设置');
    this.addOutput('onConfigured', 'trigger', '配置完成');
    this.addOutput('onError', 'trigger', '配置失败');
  }

  public execute(inputs?: any): any {
    try {
      const configureTrigger = inputs?.configure;
      if (!configureTrigger) {
        return this.getDefaultOutputs();
      }

      const viewport = inputs?.viewport || globalSceneEditingManager.getViewport();
      const backgroundColor = inputs?.backgroundColor;
      const showGrid = inputs?.showGrid as boolean;
      const showAxes = inputs?.showAxes as boolean;
      const wireframe = inputs?.wireframe as boolean;
      const shadows = inputs?.shadows as boolean;

      if (!viewport) {
        throw new Error('未提供视口对象');
      }

      const settings = {
        backgroundColor,
        showGrid,
        showAxes,
        wireframe,
        shadows
      };

      // 应用设置到渲染器
      if (viewport.renderer && backgroundColor) {
        viewport.renderer.setClearColor(backgroundColor);
      }

      if (viewport.renderer && shadows !== undefined) {
        viewport.renderer.shadowMap.enabled = shadows;
      }

      Debug.log('ViewportSettingsNode', '视口设置配置完成');

      return {
        viewport,
        settings,
        onConfigured: true,
        onError: false
      };

    } catch (error) {
      Debug.error('ViewportSettingsNode', '视口设置配置失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      viewport: null,
      settings: {},
      onConfigured: false,
      onError: false
    };
  }
}
