/**
 * 场景编辑节点 - 第二部分
 * 继续实现批次3.1的场景编辑节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Object3D, Vector3, Box3 } from 'three';
import { globalSceneEditingManager } from './SceneEditingNodes';

/**
 * 对象分组节点
 */
export class ObjectGroupingNode extends VisualScriptNode {
  public static readonly TYPE = 'ObjectGrouping';
  public static readonly NAME = '对象分组';
  public static readonly DESCRIPTION = '将多个对象组合成一个组';

  constructor(nodeType: string = ObjectGroupingNode.TYPE, name: string = ObjectGroupingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('group', 'trigger', '创建分组');
    this.addInput('ungroup', 'trigger', '解除分组');
    this.addInput('objects', 'array', '要分组的对象');
    this.addInput('groupName', 'string', '分组名称');
    this.addInput('parent', 'object', '父对象');

    // 输出端口
    this.addOutput('group', 'object', '分组对象');
    this.addOutput('groupedObjects', 'array', '分组的对象');
    this.addOutput('ungroupedObjects', 'array', '解除分组的对象');
    this.addOutput('onGrouped', 'trigger', '分组完成');
    this.addOutput('onUngrouped', 'trigger', '解除分组完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const groupTrigger = inputs?.group;
      const ungroupTrigger = inputs?.ungroup;
      const objects = inputs?.objects as Object3D[] || globalSceneEditingManager.getSelectedObjects();
      const groupName = inputs?.groupName as string || 'Group';
      const parent = inputs?.parent as Object3D;

      if (groupTrigger) {
        if (objects.length < 2) {
          throw new Error('至少需要两个对象才能创建分组');
        }

        const group = globalSceneEditingManager.groupObjects(objects, groupName);
        
        if (parent) {
          parent.add(group);
        }

        Debug.log('ObjectGroupingNode', `对象分组完成: ${groupName}, ${objects.length} 个对象`);

        return {
          group,
          groupedObjects: objects,
          ungroupedObjects: [],
          onGrouped: true,
          onUngrouped: false,
          onError: false
        };
      }

      if (ungroupTrigger) {
        const ungroupedObjects: Object3D[] = [];
        
        objects.forEach(obj => {
          if (obj.children.length > 0) {
            const children = [...obj.children];
            children.forEach(child => {
              obj.remove(child);
              if (obj.parent) {
                obj.parent.add(child);
              }
              ungroupedObjects.push(child);
            });
          }
        });

        Debug.log('ObjectGroupingNode', `对象解除分组完成: ${ungroupedObjects.length} 个对象`);

        return {
          group: null,
          groupedObjects: [],
          ungroupedObjects,
          onGrouped: false,
          onUngrouped: true,
          onError: false
        };
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ObjectGroupingNode', '对象分组操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      group: null,
      groupedObjects: [],
      ungroupedObjects: [],
      onGrouped: false,
      onUngrouped: false,
      onError: false
    };
  }
}

/**
 * 对象图层节点
 */
export class ObjectLayerNode extends VisualScriptNode {
  public static readonly TYPE = 'ObjectLayer';
  public static readonly NAME = '对象图层';
  public static readonly DESCRIPTION = '管理对象的图层属性';

  constructor(nodeType: string = ObjectLayerNode.TYPE, name: string = ObjectLayerNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('setLayer', 'trigger', '设置图层');
    this.addInput('getLayer', 'trigger', '获取图层');
    this.addInput('object', 'object', '目标对象');
    this.addInput('layer', 'number', '图层编号');
    this.addInput('layerName', 'string', '图层名称');
    this.addInput('visible', 'boolean', '可见性');

    // 输出端口
    this.addOutput('object', 'object', '对象');
    this.addOutput('currentLayer', 'number', '当前图层');
    this.addOutput('layerName', 'string', '图层名称');
    this.addOutput('isVisible', 'boolean', '是否可见');
    this.addOutput('onSet', 'trigger', '设置完成');
    this.addOutput('onGet', 'trigger', '获取完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const setTrigger = inputs?.setLayer;
      const getTrigger = inputs?.getLayer;
      const object = inputs?.object as Object3D;
      const layer = inputs?.layer as number;
      const layerName = inputs?.layerName as string;
      const visible = inputs?.visible as boolean;

      if (!object) {
        throw new Error('未提供目标对象');
      }

      if (setTrigger) {
        if (layer !== undefined) {
          object.layers.set(layer);
        }
        
        if (layerName !== undefined) {
          object.userData.layerName = layerName;
        }
        
        if (visible !== undefined) {
          object.visible = visible;
        }

        Debug.log('ObjectLayerNode', `对象图层设置完成: ${object.name || object.uuid}`);

        return {
          object,
          currentLayer: object.layers.mask,
          layerName: object.userData.layerName || '',
          isVisible: object.visible,
          onSet: true,
          onGet: false,
          onError: false
        };
      }

      if (getTrigger) {
        Debug.log('ObjectLayerNode', `对象图层获取完成: ${object.name || object.uuid}`);

        return {
          object,
          currentLayer: object.layers.mask,
          layerName: object.userData.layerName || '',
          isVisible: object.visible,
          onSet: false,
          onGet: true,
          onError: false
        };
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ObjectLayerNode', '对象图层操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      object: null,
      currentLayer: 0,
      layerName: '',
      isVisible: true,
      onSet: false,
      onGet: false,
      onError: false
    };
  }
}

/**
 * 网格吸附节点
 */
export class GridSnapNode extends VisualScriptNode {
  public static readonly TYPE = 'GridSnap';
  public static readonly NAME = '网格吸附';
  public static readonly DESCRIPTION = '启用和配置网格吸附功能';

  constructor(nodeType: string = GridSnapNode.TYPE, name: string = GridSnapNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('enable', 'trigger', '启用吸附');
    this.addInput('disable', 'trigger', '禁用吸附');
    this.addInput('snap', 'trigger', '执行吸附');
    this.addInput('enabled', 'boolean', '是否启用');
    this.addInput('snapSize', 'number', '吸附大小');
    this.addInput('object', 'object', '目标对象');

    // 输出端口
    this.addOutput('isEnabled', 'boolean', '是否启用');
    this.addOutput('currentSnapSize', 'number', '当前吸附大小');
    this.addOutput('snappedObject', 'object', '吸附后的对象');
    this.addOutput('snappedPosition', 'vector3', '吸附后的位置');
    this.addOutput('onEnabled', 'trigger', '启用完成');
    this.addOutput('onDisabled', 'trigger', '禁用完成');
    this.addOutput('onSnapped', 'trigger', '吸附完成');
  }

  public execute(inputs?: any): any {
    try {
      const enableTrigger = inputs?.enable;
      const disableTrigger = inputs?.disable;
      const snapTrigger = inputs?.snap;
      const enabled = inputs?.enabled as boolean;
      const snapSize = inputs?.snapSize as number || 1.0;
      const object = inputs?.object as Object3D;

      if (enableTrigger || (enabled !== undefined && enabled)) {
        globalSceneEditingManager.setGridSnap(true, snapSize);
        
        Debug.log('GridSnapNode', `网格吸附启用: 大小 ${snapSize}`);
        
        return {
          isEnabled: true,
          currentSnapSize: snapSize,
          snappedObject: null,
          snappedPosition: new Vector3(),
          onEnabled: true,
          onDisabled: false,
          onSnapped: false
        };
      }

      if (disableTrigger || (enabled !== undefined && !enabled)) {
        globalSceneEditingManager.setGridSnap(false, snapSize);
        
        Debug.log('GridSnapNode', '网格吸附禁用');
        
        return {
          isEnabled: false,
          currentSnapSize: snapSize,
          snappedObject: null,
          snappedPosition: new Vector3(),
          onEnabled: false,
          onDisabled: true,
          onSnapped: false
        };
      }

      if (snapTrigger && object) {
        // 执行网格吸附
        const snappedPosition = new Vector3(
          Math.round(object.position.x / snapSize) * snapSize,
          Math.round(object.position.y / snapSize) * snapSize,
          Math.round(object.position.z / snapSize) * snapSize
        );
        
        object.position.copy(snappedPosition);
        
        Debug.log('GridSnapNode', `对象吸附完成: ${object.name || object.uuid}`);
        
        return {
          isEnabled: true,
          currentSnapSize: snapSize,
          snappedObject: object,
          snappedPosition,
          onEnabled: false,
          onDisabled: false,
          onSnapped: true
        };
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('GridSnapNode', '网格吸附操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      isEnabled: false,
      currentSnapSize: 1.0,
      snappedObject: null,
      snappedPosition: new Vector3(),
      onEnabled: false,
      onDisabled: false,
      onSnapped: false
    };
  }
}

/**
 * 对象对齐节点
 */
export class ObjectAlignmentNode extends VisualScriptNode {
  public static readonly TYPE = 'ObjectAlignment';
  public static readonly NAME = '对象对齐';
  public static readonly DESCRIPTION = '对齐多个对象的位置';

  constructor(nodeType: string = ObjectAlignmentNode.TYPE, name: string = ObjectAlignmentNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('align', 'trigger', '执行对齐');
    this.addInput('objects', 'array', '要对齐的对象');
    this.addInput('alignment', 'string', '对齐方式');
    this.addInput('axis', 'string', '对齐轴');

    // 输出端口
    this.addOutput('alignedObjects', 'array', '对齐后的对象');
    this.addOutput('alignmentType', 'string', '对齐类型');
    this.addOutput('onAligned', 'trigger', '对齐完成');
    this.addOutput('onError', 'trigger', '对齐失败');
  }

  public execute(inputs?: any): any {
    try {
      const alignTrigger = inputs?.align;
      if (!alignTrigger) {
        return this.getDefaultOutputs();
      }

      const objects = inputs?.objects as Object3D[] || globalSceneEditingManager.getSelectedObjects();
      const alignment = inputs?.alignment as string || 'center';
      const axis = inputs?.axis as string || 'x';

      if (objects.length < 2) {
        throw new Error('至少需要两个对象才能执行对齐');
      }

      globalSceneEditingManager.alignObjects(objects, alignment);

      Debug.log('ObjectAlignmentNode', `对象对齐完成: ${alignment}, ${objects.length} 个对象`);

      return {
        alignedObjects: objects,
        alignmentType: alignment,
        onAligned: true,
        onError: false
      };

    } catch (error) {
      Debug.error('ObjectAlignmentNode', '对象对齐失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      alignedObjects: [],
      alignmentType: '',
      onAligned: false,
      onError: false
    };
  }
}

/**
 * 对象分布节点
 */
export class ObjectDistributionNode extends VisualScriptNode {
  public static readonly TYPE = 'ObjectDistribution';
  public static readonly NAME = '对象分布';
  public static readonly DESCRIPTION = '均匀分布多个对象';

  constructor(nodeType: string = ObjectDistributionNode.TYPE, name: string = ObjectDistributionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('distribute', 'trigger', '执行分布');
    this.addInput('objects', 'array', '要分布的对象');
    this.addInput('axis', 'string', '分布轴');
    this.addInput('spacing', 'number', '间距');
    this.addInput('startPosition', 'vector3', '起始位置');
    this.addInput('endPosition', 'vector3', '结束位置');

    // 输出端口
    this.addOutput('distributedObjects', 'array', '分布后的对象');
    this.addOutput('totalSpacing', 'number', '总间距');
    this.addOutput('onDistributed', 'trigger', '分布完成');
    this.addOutput('onError', 'trigger', '分布失败');
  }

  public execute(inputs?: any): any {
    try {
      const distributeTrigger = inputs?.distribute;
      if (!distributeTrigger) {
        return this.getDefaultOutputs();
      }

      const objects = inputs?.objects as Object3D[] || globalSceneEditingManager.getSelectedObjects();
      const axis = inputs?.axis as string || 'x';
      const spacing = inputs?.spacing as number;
      const startPosition = inputs?.startPosition as Vector3;
      const endPosition = inputs?.endPosition as Vector3;

      if (objects.length < 2) {
        throw new Error('至少需要两个对象才能执行分布');
      }

      let totalSpacing = 0;

      if (spacing !== undefined) {
        // 按固定间距分布
        objects.forEach((obj, index) => {
          switch (axis) {
            case 'x':
              obj.position.x = (startPosition?.x || 0) + index * spacing;
              break;
            case 'y':
              obj.position.y = (startPosition?.y || 0) + index * spacing;
              break;
            case 'z':
              obj.position.z = (startPosition?.z || 0) + index * spacing;
              break;
          }
        });
        totalSpacing = spacing * (objects.length - 1);
      } else if (startPosition && endPosition) {
        // 在起始和结束位置之间均匀分布
        const distance = startPosition.distanceTo(endPosition);
        const step = distance / (objects.length - 1);
        const direction = new Vector3().subVectors(endPosition, startPosition).normalize();

        objects.forEach((obj, index) => {
          const position = new Vector3().copy(startPosition).add(
            direction.clone().multiplyScalar(step * index)
          );
          obj.position.copy(position);
        });
        totalSpacing = distance;
      }

      Debug.log('ObjectDistributionNode', `对象分布完成: ${axis} 轴, ${objects.length} 个对象`);

      return {
        distributedObjects: objects,
        totalSpacing,
        onDistributed: true,
        onError: false
      };

    } catch (error) {
      Debug.error('ObjectDistributionNode', '对象分布失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      distributedObjects: [],
      totalSpacing: 0,
      onDistributed: false,
      onError: false
    };
  }
}
