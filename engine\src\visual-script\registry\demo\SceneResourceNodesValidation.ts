/**
 * 场景与资源管理节点验证脚本
 * 验证批次2注册的55个节点是否正确实现
 */
import { Debug } from '../../../utils/Debug';

/**
 * 场景与资源管理节点验证类
 */
export class SceneResourceNodesValidation {
  private validationResults: Map<string, boolean> = new Map();
  private errors: string[] = [];

  /**
   * 运行完整验证
   */
  public async runValidation(): Promise<void> {
    Debug.log('SceneResourceNodesValidation', '开始验证场景与资源管理节点...');

    try {
      // 1. 验证场景编辑节点（15个）
      await this.validateSceneEditingNodes();

      // 2. 验证场景管理节点（7个）
      await this.validateSceneManagementNodes();

      // 3. 验证资源加载节点（13个）
      await this.validateResourceLoadingNodes();

      // 4. 验证资源优化节点（9个）
      await this.validateResourceOptimizationNodes();

      // 5. 生成验证报告
      this.generateValidationReport();

      Debug.log('SceneResourceNodesValidation', '场景与资源管理节点验证完成');
    } catch (error) {
      Debug.error('SceneResourceNodesValidation', '验证过程中发生错误:', error);
    }
  }

  /**
   * 验证场景编辑节点（15个）
   */
  private async validateSceneEditingNodes(): Promise<void> {
    Debug.log('SceneResourceNodesValidation', '验证场景编辑节点...');

    const sceneEditingNodes = [
      'SceneViewportNode',
      'ObjectSelectionNode', 
      'ObjectTransformNode',
      'ObjectDuplicationNode',
      'ObjectGroupingNode',
      'ObjectLayerNode',
      'GridSnapNode',
      'ObjectAlignmentNode',
      'ObjectDistributionNode',
      'UndoRedoNode',
      'HistoryManagementNode',
      'SelectionFilterNode',
      'ViewportNavigationNode',
      'ViewportRenderingNode',
      'ViewportSettingsNode'
    ];

    for (const nodeName of sceneEditingNodes) {
      try {
        // 动态导入节点类
        const nodeClass = await this.importNodeClass(nodeName);
        const isValid = this.validateNodeClass(nodeClass, nodeName);
        this.validationResults.set(nodeName, isValid);
        
        if (isValid) {
          Debug.log('SceneResourceNodesValidation', `✓ ${nodeName} 验证通过`);
        } else {
          Debug.warn('SceneResourceNodesValidation', `✗ ${nodeName} 验证失败`);
        }
      } catch (error) {
        this.validationResults.set(nodeName, false);
        this.errors.push(`${nodeName}: ${error.message}`);
        Debug.error('SceneResourceNodesValidation', `✗ ${nodeName} 导入失败:`, error);
      }
    }
  }

  /**
   * 验证场景管理节点（7个）
   */
  private async validateSceneManagementNodes(): Promise<void> {
    Debug.log('SceneResourceNodesValidation', '验证场景管理节点...');

    const sceneManagementNodes = [
      'LoadSceneNode',
      'SaveSceneNode',
      'CreateSceneNode',
      'DestroySceneNode',
      'AddObjectToSceneNode',
      'RemoveObjectFromSceneNode',
      'FindSceneObjectNode'
    ];

    for (const nodeName of sceneManagementNodes) {
      try {
        const nodeClass = await this.importNodeClass(nodeName);
        const isValid = this.validateNodeClass(nodeClass, nodeName);
        this.validationResults.set(nodeName, isValid);
        
        if (isValid) {
          Debug.log('SceneResourceNodesValidation', `✓ ${nodeName} 验证通过`);
        } else {
          Debug.warn('SceneResourceNodesValidation', `✗ ${nodeName} 验证失败`);
        }
      } catch (error) {
        this.validationResults.set(nodeName, false);
        this.errors.push(`${nodeName}: ${error.message}`);
        Debug.error('SceneResourceNodesValidation', `✗ ${nodeName} 导入失败:`, error);
      }
    }
  }

  /**
   * 验证资源加载节点（13个）
   */
  private async validateResourceLoadingNodes(): Promise<void> {
    Debug.log('SceneResourceNodesValidation', '验证资源加载节点...');

    const resourceLoadingNodes = [
      'LoadAssetNode',
      'UnloadAssetNode',
      'PreloadAssetNode',
      'AsyncLoadAssetNode',
      'LoadAssetBundleNode',
      'AssetDependencyNode',
      'AssetCacheNode',
      'AssetCompressionNode',
      'AssetEncryptionNode',
      'AssetValidationNode',
      'AssetMetadataNode',
      'AssetVersionNode',
      'AssetOptimizationNode'
    ];

    for (const nodeName of resourceLoadingNodes) {
      try {
        const nodeClass = await this.importNodeClass(nodeName);
        const isValid = this.validateNodeClass(nodeClass, nodeName);
        this.validationResults.set(nodeName, isValid);
        
        if (isValid) {
          Debug.log('SceneResourceNodesValidation', `✓ ${nodeName} 验证通过`);
        } else {
          Debug.warn('SceneResourceNodesValidation', `✗ ${nodeName} 验证失败`);
        }
      } catch (error) {
        this.validationResults.set(nodeName, false);
        this.errors.push(`${nodeName}: ${error.message}`);
        Debug.error('SceneResourceNodesValidation', `✗ ${nodeName} 导入失败:`, error);
      }
    }
  }

  /**
   * 验证资源优化节点（9个）
   */
  private async validateResourceOptimizationNodes(): Promise<void> {
    Debug.log('SceneResourceNodesValidation', '验证资源优化节点...');

    const resourceOptimizationNodes = [
      'TextureCompressionNode',
      'MeshOptimizationNode',
      'AudioCompressionNode',
      'AssetBatchingNode',
      'AssetStreamingNode',
      'AssetMemoryManagementNode',
      'AssetGarbageCollectionNode',
      'AssetPerformanceMonitorNode',
      'AssetUsageAnalyticsNode'
    ];

    for (const nodeName of resourceOptimizationNodes) {
      try {
        const nodeClass = await this.importNodeClass(nodeName);
        const isValid = this.validateNodeClass(nodeClass, nodeName);
        this.validationResults.set(nodeName, isValid);
        
        if (isValid) {
          Debug.log('SceneResourceNodesValidation', `✓ ${nodeName} 验证通过`);
        } else {
          Debug.warn('SceneResourceNodesValidation', `✗ ${nodeName} 验证失败`);
        }
      } catch (error) {
        this.validationResults.set(nodeName, false);
        this.errors.push(`${nodeName}: ${error.message}`);
        Debug.error('SceneResourceNodesValidation', `✗ ${nodeName} 导入失败:`, error);
      }
    }
  }

  /**
   * 动态导入节点类
   */
  private async importNodeClass(nodeName: string): Promise<any> {
    // 根据节点名称确定导入路径
    let importPath: string;
    
    if (nodeName.includes('Scene') || nodeName.includes('Object') || 
        nodeName.includes('Grid') || nodeName.includes('Undo') || 
        nodeName.includes('History') || nodeName.includes('Selection') || 
        nodeName.includes('Viewport')) {
      // 场景编辑节点
      if (nodeName.includes('Viewport') && !nodeName.includes('Scene')) {
        importPath = '../nodes/scene/SceneEditingNodes3';
      } else if (nodeName.includes('Object') && 
                 ['ObjectGroupingNode', 'ObjectLayerNode', 'GridSnapNode', 
                  'ObjectAlignmentNode', 'ObjectDistributionNode'].includes(nodeName)) {
        importPath = '../nodes/scene/SceneEditingNodes2';
      } else if (nodeName.includes('Undo') || nodeName.includes('History') || 
                 nodeName.includes('Selection') || nodeName.includes('Viewport')) {
        importPath = '../nodes/scene/SceneEditingNodes3';
      } else if (nodeName.includes('Scene') && 
                 ['LoadSceneNode', 'SaveSceneNode', 'CreateSceneNode', 'DestroySceneNode',
                  'AddObjectToSceneNode', 'RemoveObjectFromSceneNode', 'FindSceneObjectNode'].includes(nodeName)) {
        importPath = '../nodes/scene/SceneManagementNodes';
      } else {
        importPath = '../nodes/scene/SceneEditingNodes';
      }
    } else if (nodeName.includes('Asset') || nodeName.includes('Load') || 
               nodeName.includes('Preload') || nodeName.includes('Async')) {
      // 资源管理节点
      importPath = '../nodes/resources/ResourceManagementNodes';
    } else if (nodeName.includes('Texture') || nodeName.includes('Mesh') || 
               nodeName.includes('Audio') || nodeName.includes('Batching') || 
               nodeName.includes('Streaming') || nodeName.includes('Memory') || 
               nodeName.includes('Garbage') || nodeName.includes('Performance') || 
               nodeName.includes('Usage')) {
      // 资源优化节点
      importPath = '../nodes/resources/ResourceOptimizationNodes';
    } else {
      throw new Error(`未知的节点类型: ${nodeName}`);
    }

    try {
      const module = await import(importPath);
      const nodeClass = module[nodeName];
      
      if (!nodeClass) {
        throw new Error(`节点类 ${nodeName} 在模块 ${importPath} 中未找到`);
      }
      
      return nodeClass;
    } catch (error) {
      throw new Error(`导入节点类 ${nodeName} 失败: ${error.message}`);
    }
  }

  /**
   * 验证节点类
   */
  private validateNodeClass(nodeClass: any, nodeName: string): boolean {
    try {
      // 检查静态属性
      if (!nodeClass.TYPE) {
        this.errors.push(`${nodeName}: 缺少静态属性 TYPE`);
        return false;
      }
      
      if (!nodeClass.NAME) {
        this.errors.push(`${nodeName}: 缺少静态属性 NAME`);
        return false;
      }
      
      if (!nodeClass.DESCRIPTION) {
        this.errors.push(`${nodeName}: 缺少静态属性 DESCRIPTION`);
        return false;
      }

      // 检查构造函数
      try {
        const instance = new nodeClass();
        
        // 检查实例方法
        if (typeof instance.execute !== 'function') {
          this.errors.push(`${nodeName}: 缺少 execute 方法`);
          return false;
        }
        
        return true;
      } catch (error) {
        this.errors.push(`${nodeName}: 实例化失败 - ${error.message}`);
        return false;
      }
    } catch (error) {
      this.errors.push(`${nodeName}: 验证失败 - ${error.message}`);
      return false;
    }
  }

  /**
   * 生成验证报告
   */
  private generateValidationReport(): void {
    Debug.log('SceneResourceNodesValidation', '=== 场景与资源管理节点验证报告 ===');
    
    const totalNodes = this.validationResults.size;
    const validNodes = Array.from(this.validationResults.values()).filter(v => v).length;
    const invalidNodes = totalNodes - validNodes;
    
    Debug.log('SceneResourceNodesValidation', `总节点数: ${totalNodes}`);
    Debug.log('SceneResourceNodesValidation', `验证通过: ${validNodes}`);
    Debug.log('SceneResourceNodesValidation', `验证失败: ${invalidNodes}`);
    Debug.log('SceneResourceNodesValidation', `成功率: ${((validNodes / totalNodes) * 100).toFixed(2)}%`);
    
    if (this.errors.length > 0) {
      Debug.log('SceneResourceNodesValidation', '=== 错误详情 ===');
      for (const error of this.errors) {
        Debug.error('SceneResourceNodesValidation', error);
      }
    }
    
    Debug.log('SceneResourceNodesValidation', '=== 验证报告完成 ===');
  }

  /**
   * 获取验证结果
   */
  public getValidationResults(): {
    total: number;
    valid: number;
    invalid: number;
    successRate: number;
    errors: string[];
  } {
    const totalNodes = this.validationResults.size;
    const validNodes = Array.from(this.validationResults.values()).filter(v => v).length;
    const invalidNodes = totalNodes - validNodes;
    
    return {
      total: totalNodes,
      valid: validNodes,
      invalid: invalidNodes,
      successRate: (validNodes / totalNodes) * 100,
      errors: [...this.errors]
    };
  }
}

/**
 * 运行验证
 */
export async function runSceneResourceNodesValidation(): Promise<void> {
  const validation = new SceneResourceNodesValidation();
  await validation.runValidation();
  return validation.getValidationResults();
}
