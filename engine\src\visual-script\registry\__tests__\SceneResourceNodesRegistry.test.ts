/**
 * 场景与资源管理节点注册表测试
 */
import { describe, test, expect, beforeEach, vi } from 'vitest';

// Mock all dependencies first
vi.mock('../NodeRegistry', () => ({
  NodeRegistry: {
    getInstance: vi.fn(() => ({
      registerNode: vi.fn().mockResolvedValue(undefined),
      createNode: vi.fn().mockReturnValue({
        execute: vi.fn().mockReturnValue({ success: true })
      })
    }))
  },
  NodeCategory: {
    SCENE_EDITING: 'scene_editing',
    SCENE_MANAGEMENT: 'scene_management',
    RESOURCE_MANAGEMENT: 'resource_management',
    RESOURCE_OPTIMIZATION: 'resource_optimization'
  },
  createNodeInfo: vi.fn((nodeClass, category, subcategory, tags) => ({
    type: nodeClass.TYPE,
    name: nodeClass.NAME,
    description: nodeClass.DESCRIPTION,
    category,
    subcategory,
    tags,
    nodeClass
  }))
}));

// Mock Debug
vi.mock('../../utils/Debug', () => ({
  Debug: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn()
  }
}));

// Mock VisualScriptNode
vi.mock('../../../visualscript/VisualScriptNode', () => ({
  VisualScriptNode: class MockVisualScriptNode {
    static TYPE = 'MockNode';
    static NAME = 'Mock Node';
    static DESCRIPTION = 'Mock Description';

    constructor(nodeType = 'MockNode', name = 'Mock Node', id) {
      this.nodeType = nodeType;
      this.name = name;
      this.id = id || 'mock-id';
    }

    execute() {
      return { success: true };
    }
  }
}));

// Mock all node imports
vi.mock('../nodes/scene/SceneEditingNodes', () => ({
  SceneViewportNode: class { static TYPE = 'SceneViewport'; static NAME = '场景视口'; static DESCRIPTION = '管理场景视口'; },
  ObjectSelectionNode: class { static TYPE = 'ObjectSelection'; static NAME = '对象选择'; static DESCRIPTION = '选择对象'; },
  ObjectTransformNode: class { static TYPE = 'ObjectTransform'; static NAME = '对象变换'; static DESCRIPTION = '变换对象'; },
  ObjectDuplicationNode: class { static TYPE = 'ObjectDuplication'; static NAME = '对象复制'; static DESCRIPTION = '复制对象'; }
}));

vi.mock('../nodes/scene/SceneEditingNodes2', () => ({
  ObjectGroupingNode: class { static TYPE = 'ObjectGrouping'; static NAME = '对象分组'; static DESCRIPTION = '分组对象'; },
  ObjectLayerNode: class { static TYPE = 'ObjectLayer'; static NAME = '对象图层'; static DESCRIPTION = '管理图层'; },
  GridSnapNode: class { static TYPE = 'GridSnap'; static NAME = '网格吸附'; static DESCRIPTION = '网格吸附'; },
  ObjectAlignmentNode: class { static TYPE = 'ObjectAlignment'; static NAME = '对象对齐'; static DESCRIPTION = '对齐对象'; },
  ObjectDistributionNode: class { static TYPE = 'ObjectDistribution'; static NAME = '对象分布'; static DESCRIPTION = '分布对象'; }
}));

vi.mock('../nodes/scene/SceneEditingNodes3', () => ({
  UndoRedoNode: class { static TYPE = 'UndoRedo'; static NAME = '撤销重做'; static DESCRIPTION = '撤销重做'; },
  HistoryManagementNode: class { static TYPE = 'HistoryManagement'; static NAME = '历史管理'; static DESCRIPTION = '历史管理'; },
  SelectionFilterNode: class { static TYPE = 'SelectionFilter'; static NAME = '选择过滤'; static DESCRIPTION = '过滤选择'; },
  ViewportNavigationNode: class { static TYPE = 'ViewportNavigation'; static NAME = '视口导航'; static DESCRIPTION = '导航视口'; },
  ViewportRenderingNode: class { static TYPE = 'ViewportRendering'; static NAME = '视口渲染'; static DESCRIPTION = '渲染视口'; },
  ViewportSettingsNode: class { static TYPE = 'ViewportSettings'; static NAME = '视口设置'; static DESCRIPTION = '设置视口'; }
}));

vi.mock('../nodes/scene/SceneManagementNodes', () => ({
  LoadSceneNode: class { static TYPE = 'LoadScene'; static NAME = '加载场景'; static DESCRIPTION = '加载场景'; },
  SaveSceneNode: class { static TYPE = 'SaveScene'; static NAME = '保存场景'; static DESCRIPTION = '保存场景'; },
  CreateSceneNode: class { static TYPE = 'CreateScene'; static NAME = '创建场景'; static DESCRIPTION = '创建场景'; },
  DestroySceneNode: class { static TYPE = 'DestroyScene'; static NAME = '销毁场景'; static DESCRIPTION = '销毁场景'; },
  AddObjectToSceneNode: class { static TYPE = 'AddObjectToScene'; static NAME = '添加对象'; static DESCRIPTION = '添加对象'; },
  RemoveObjectFromSceneNode: class { static TYPE = 'RemoveObjectFromScene'; static NAME = '移除对象'; static DESCRIPTION = '移除对象'; },
  FindSceneObjectNode: class { static TYPE = 'FindSceneObject'; static NAME = '查找对象'; static DESCRIPTION = '查找对象'; }
}));

vi.mock('../nodes/resources/ResourceManagementNodes', () => ({
  LoadAssetNode: class { static TYPE = 'LoadAsset'; static NAME = '加载资源'; static DESCRIPTION = '加载资源'; },
  UnloadAssetNode: class { static TYPE = 'UnloadAsset'; static NAME = '卸载资源'; static DESCRIPTION = '卸载资源'; },
  PreloadAssetNode: class { static TYPE = 'PreloadAsset'; static NAME = '预加载资源'; static DESCRIPTION = '预加载资源'; },
  AsyncLoadAssetNode: class { static TYPE = 'AsyncLoadAsset'; static NAME = '异步加载'; static DESCRIPTION = '异步加载'; },
  LoadAssetBundleNode: class { static TYPE = 'LoadAssetBundle'; static NAME = '加载资源包'; static DESCRIPTION = '加载资源包'; },
  AssetDependencyNode: class { static TYPE = 'AssetDependency'; static NAME = '资源依赖'; static DESCRIPTION = '资源依赖'; },
  AssetCacheNode: class { static TYPE = 'AssetCache'; static NAME = '资源缓存'; static DESCRIPTION = '资源缓存'; },
  AssetCompressionNode: class { static TYPE = 'AssetCompression'; static NAME = '资源压缩'; static DESCRIPTION = '资源压缩'; },
  AssetEncryptionNode: class { static TYPE = 'AssetEncryption'; static NAME = '资源加密'; static DESCRIPTION = '资源加密'; },
  AssetValidationNode: class { static TYPE = 'AssetValidation'; static NAME = '资源验证'; static DESCRIPTION = '资源验证'; },
  AssetMetadataNode: class { static TYPE = 'AssetMetadata'; static NAME = '资源元数据'; static DESCRIPTION = '资源元数据'; },
  AssetVersionNode: class { static TYPE = 'AssetVersion'; static NAME = '资源版本'; static DESCRIPTION = '资源版本'; },
  AssetOptimizationNode: class { static TYPE = 'AssetOptimization'; static NAME = '资源优化'; static DESCRIPTION = '资源优化'; }
}));

vi.mock('../nodes/resources/ResourceOptimizationNodes', () => ({
  TextureCompressionNode: class { static TYPE = 'TextureCompression'; static NAME = '纹理压缩'; static DESCRIPTION = '纹理压缩'; },
  MeshOptimizationNode: class { static TYPE = 'MeshOptimization'; static NAME = '网格优化'; static DESCRIPTION = '网格优化'; },
  AudioCompressionNode: class { static TYPE = 'AudioCompression'; static NAME = '音频压缩'; static DESCRIPTION = '音频压缩'; },
  AssetBatchingNode: class { static TYPE = 'AssetBatching'; static NAME = '资源批处理'; static DESCRIPTION = '资源批处理'; },
  AssetStreamingNode: class { static TYPE = 'AssetStreaming'; static NAME = '资源流传输'; static DESCRIPTION = '资源流传输'; },
  AssetMemoryManagementNode: class { static TYPE = 'AssetMemoryManagement'; static NAME = '内存管理'; static DESCRIPTION = '内存管理'; },
  AssetGarbageCollectionNode: class { static TYPE = 'AssetGarbageCollection'; static NAME = '垃圾回收'; static DESCRIPTION = '垃圾回收'; },
  AssetPerformanceMonitorNode: class { static TYPE = 'AssetPerformanceMonitor'; static NAME = '性能监控'; static DESCRIPTION = '性能监控'; },
  AssetUsageAnalyticsNode: class { static TYPE = 'AssetUsageAnalytics'; static NAME = '使用分析'; static DESCRIPTION = '使用分析'; }
}));

import { SceneResourceNodesRegistry } from '../SceneResourceNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';

describe('SceneResourceNodesRegistry', () => {
  let registry: SceneResourceNodesRegistry;
  let mockNodeRegistry: any;

  beforeEach(() => {
    vi.clearAllMocks();
    registry = SceneResourceNodesRegistry.getInstance();
    mockNodeRegistry = NodeRegistry.getInstance();
  });

  test('应该是单例模式', () => {
    const instance1 = SceneResourceNodesRegistry.getInstance();
    const instance2 = SceneResourceNodesRegistry.getInstance();
    expect(instance1).toBe(instance2);
  });

  test('应该成功注册所有55个节点', async () => {
    await registry.registerAllNodes();
    
    // 验证registerNode被调用了55次
    expect(mockNodeRegistry.registerNode).toHaveBeenCalledTimes(55);
    
    // 验证注册的节点数量
    const registeredNodes = registry.getRegisteredNodes();
    expect(registeredNodes).toHaveLength(55);
  });

  test('应该正确注册场景编辑节点', async () => {
    await registry.registerAllNodes();
    
    // 验证特定的场景编辑节点是否已注册
    expect(registry.isNodeRegistered('SceneViewport')).toBe(true);
    expect(registry.isNodeRegistered('ObjectSelection')).toBe(true);
    expect(registry.isNodeRegistered('ObjectTransform')).toBe(true);
    expect(registry.isNodeRegistered('ObjectDuplication')).toBe(true);
    expect(registry.isNodeRegistered('ObjectGrouping')).toBe(true);
    expect(registry.isNodeRegistered('ObjectLayer')).toBe(true);
    expect(registry.isNodeRegistered('GridSnap')).toBe(true);
    expect(registry.isNodeRegistered('ObjectAlignment')).toBe(true);
    expect(registry.isNodeRegistered('ObjectDistribution')).toBe(true);
    expect(registry.isNodeRegistered('UndoRedo')).toBe(true);
    expect(registry.isNodeRegistered('HistoryManagement')).toBe(true);
    expect(registry.isNodeRegistered('SelectionFilter')).toBe(true);
    expect(registry.isNodeRegistered('ViewportNavigation')).toBe(true);
    expect(registry.isNodeRegistered('ViewportRendering')).toBe(true);
    expect(registry.isNodeRegistered('ViewportSettings')).toBe(true);
  });

  test('应该正确注册场景管理节点', async () => {
    await registry.registerAllNodes();
    
    // 验证场景管理节点是否已注册
    expect(registry.isNodeRegistered('LoadScene')).toBe(true);
    expect(registry.isNodeRegistered('SaveScene')).toBe(true);
    expect(registry.isNodeRegistered('CreateScene')).toBe(true);
    expect(registry.isNodeRegistered('DestroyScene')).toBe(true);
    expect(registry.isNodeRegistered('AddObjectToScene')).toBe(true);
    expect(registry.isNodeRegistered('RemoveObjectFromScene')).toBe(true);
    expect(registry.isNodeRegistered('FindSceneObject')).toBe(true);
  });

  test('应该正确注册资源加载节点', async () => {
    await registry.registerAllNodes();
    
    // 验证资源加载节点是否已注册
    expect(registry.isNodeRegistered('LoadAsset')).toBe(true);
    expect(registry.isNodeRegistered('UnloadAsset')).toBe(true);
    expect(registry.isNodeRegistered('PreloadAsset')).toBe(true);
    expect(registry.isNodeRegistered('AsyncLoadAsset')).toBe(true);
    expect(registry.isNodeRegistered('LoadAssetBundle')).toBe(true);
    expect(registry.isNodeRegistered('AssetDependency')).toBe(true);
    expect(registry.isNodeRegistered('AssetCache')).toBe(true);
    expect(registry.isNodeRegistered('AssetCompression')).toBe(true);
    expect(registry.isNodeRegistered('AssetEncryption')).toBe(true);
    expect(registry.isNodeRegistered('AssetValidation')).toBe(true);
    expect(registry.isNodeRegistered('AssetMetadata')).toBe(true);
    expect(registry.isNodeRegistered('AssetVersion')).toBe(true);
    expect(registry.isNodeRegistered('AssetOptimization')).toBe(true);
  });

  test('应该正确注册资源优化节点', async () => {
    await registry.registerAllNodes();
    
    // 验证资源优化节点是否已注册
    expect(registry.isNodeRegistered('TextureCompression')).toBe(true);
    expect(registry.isNodeRegistered('MeshOptimization')).toBe(true);
    expect(registry.isNodeRegistered('AudioCompression')).toBe(true);
    expect(registry.isNodeRegistered('AssetBatching')).toBe(true);
    expect(registry.isNodeRegistered('AssetStreaming')).toBe(true);
    expect(registry.isNodeRegistered('AssetMemoryManagement')).toBe(true);
    expect(registry.isNodeRegistered('AssetGarbageCollection')).toBe(true);
    expect(registry.isNodeRegistered('AssetPerformanceMonitor')).toBe(true);
    expect(registry.isNodeRegistered('AssetUsageAnalytics')).toBe(true);
  });

  test('应该处理注册失败的情况', async () => {
    // 模拟注册失败
    mockNodeRegistry.registerNode.mockRejectedValueOnce(new Error('注册失败'));
    
    await expect(registry.registerAllNodes()).rejects.toThrow('注册失败');
  });

  test('应该返回正确的已注册节点列表', async () => {
    await registry.registerAllNodes();
    
    const registeredNodes = registry.getRegisteredNodes();
    expect(registeredNodes).toContain('SceneViewport');
    expect(registeredNodes).toContain('LoadScene');
    expect(registeredNodes).toContain('LoadAsset');
    expect(registeredNodes).toContain('TextureCompression');
  });

  test('应该正确检查节点是否已注册', async () => {
    await registry.registerAllNodes();
    
    expect(registry.isNodeRegistered('SceneViewport')).toBe(true);
    expect(registry.isNodeRegistered('NonExistentNode')).toBe(false);
  });
});
