/**
 * 视觉脚本节点注册系统入口
 * 统一导出所有注册相关的功能
 */

// 导出核心注册系统
export {
  NodeRegistry,
  NodeCategory,
  NodeInfo,
  initializeNodeRegistry,
  registerNodes,
  createNodeInfo,
  getNodeStatistics,
  validateNodeRegistry
} from './NodeRegistry';

// 导出节点注册实现
export {
  registerAllAvailableNodes
} from './NodeRegistrations';

// 导出批次注册表
export {
  CoreRenderingNodesRegistry,
  coreRenderingNodesRegistry
} from './CoreRenderingNodesRegistry';

export {
  SceneResourceNodesRegistry,
  sceneResourceNodesRegistry
} from './SceneResourceNodesRegistry';

import { Debug } from '../../utils/Debug';
import { NodeRegistry } from './NodeRegistry';
import { registerAllAvailableNodes } from './NodeRegistrations';

/**
 * 初始化整个视觉脚本节点系统
 * 这是系统的主要入口点
 */
export async function initializeVisualScriptSystem(): Promise<void> {
  try {
    Debug.log('VisualScriptSystem', '开始初始化视觉脚本系统...');

    // 1. 初始化节点注册表
    NodeRegistry.initialize();

    // 2. 注册所有可用的节点
    await registerAllAvailableNodes();

    // 3. 验证注册表的完整性
    const validation = validateNodeRegistry();
    if (!validation.isValid) {
      Debug.error('VisualScriptSystem', '节点注册表验证失败:', validation.errors);
      for (const warning of validation.warnings) {
        Debug.warn('VisualScriptSystem', warning);
      }
    } else {
      Debug.log('VisualScriptSystem', '节点注册表验证通过');
    }

    // 4. 输出统计信息
    const stats = getNodeStatistics();
    Debug.log('VisualScriptSystem', `系统初始化完成:
      - 总节点数: ${stats.totalNodes}
      - 分类数: ${stats.categoryCounts.size}
      - 废弃节点: ${stats.deprecatedCount}
      - 实验性节点: ${stats.experimentalCount}`);

    // 5. 输出各分类的节点数量
    for (const [category, count] of stats.categoryCounts) {
      Debug.log('VisualScriptSystem', `  ${category}: ${count}个节点`);
    }

  } catch (error) {
    Debug.error('VisualScriptSystem', '视觉脚本系统初始化失败:', error);
    throw error;
  }
}

/**
 * 获取系统状态信息
 */
export function getSystemStatus(): {
  isInitialized: boolean;
  nodeCount: number;
  categoryCount: number;
  lastError?: string;
} {
  try {
    const stats = getNodeStatistics();
    return {
      isInitialized: true,
      nodeCount: stats.totalNodes,
      categoryCount: stats.categoryCounts.size
    };
  } catch (error) {
    return {
      isInitialized: false,
      nodeCount: 0,
      categoryCount: 0,
      lastError: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * 重新加载节点注册表
 * 用于开发时的热重载
 */
export async function reloadNodeRegistry(): Promise<void> {
  try {
    Debug.log('VisualScriptSystem', '重新加载节点注册表...');

    // 清空现有注册表
    NodeRegistry.clear();

    // 重新初始化并注册所有节点
    NodeRegistry.initialize();
    await registerAllAvailableNodes();

    Debug.log('VisualScriptSystem', '节点注册表重新加载完成');
  } catch (error) {
    Debug.error('VisualScriptSystem', '节点注册表重新加载失败:', error);
    throw error;
  }
}

// 导入验证函数
import { validateNodeRegistry, getNodeStatistics } from './NodeRegistry';
